#!/usr/bin/env python3
"""
Restore the best balance ratios from the 1,000,000 bootstrap validation
"""

from enhanced_adaptive_learning_system import EnhancedAdaptiveLearningSystem

def main():
    print("🔄 Restoring Best Balance Ratios...")
    print("=" * 60)

    # Create enhanced learning system instance
    enhanced_system = EnhancedAdaptiveLearningSystem()

    # Manually create the best validation results from your 1M bootstrap run
    best_validation_results = {
        'status': 'success',
        'context_results': {
            'Clay_Set1_Mid': {
                'optimal_balance': {
                    'historical_ratio': 0.3,
                    'momentum_ratio': 0.7,
                    'accuracy': 0.720,
                    'is_statistically_significant': True
                }
            },
            'Clay_Set2_Mid': {
                'optimal_balance': {
                    'historical_ratio': 0.4,
                    'momentum_ratio': 0.6,
                    'accuracy': 0.692,
                    'is_statistically_significant': True
                }
            }
        }
    }

    print("🎯 Restoring optimal ratios:")
    print("   Clay_Set1_Mid: 0.3/0.7 (72.0% accuracy)")
    print("   Clay_Set2_Mid: 0.4/0.6 (69.2% accuracy)")

    # Apply the best ratios
    application_results = enhanced_system.apply_validated_balance_ratios(best_validation_results)
    
    if application_results.get('status') == 'success':
        print(f"🎉 Successfully restored best ratios!")
        print(f"📝 New configuration version: {application_results['new_version']}")
        
        # Show what was applied
        changes = application_results.get('changes_applied', {})
        print("\n📋 Restored Changes:")
        for context, ratio in changes.items():
            print(f"   {context}: {ratio}")
            
    else:
        print(f"❌ Failed to restore: {application_results.get('message', 'Unknown error')}")

    print("\n" + "=" * 60)
    print("✅ Restoration complete!")

if __name__ == "__main__":
    main()
